"use client"

import { useEffect, useRef } from "react"
// import useScrollActive from "@/hooks/useScrollActive"
// import Circle from "@/public/assets/about/circle.svg"
// import Signs from "@/public/assets/about/signs.svg"
// import Star from "@/public/assets/about/star.svg"
// import Triangle from "@/public/assets/about/triangle.svg"
// import ShinThantImage from "@/public/me-5.jpg"
// import { useSectionStore } from "@/store/section"
// import { gsap } from "gsap"
// import { ScrollTrigger } from "gsap/dist/ScrollTrigger"
// import Image from "next/image"
// import SplitType from "split-type"

const timelineData = [
  { year: "2018", event: "蜜多云科技，公司创办" },
  { year: "2020", event: "推出智慧门店，性能1.0" },
  { year: "2021", event: "疫情期间助力100+推出全链数字，化MEDOMC" },
  { year: "2022", event: "行业数字营销，产品持续迭代" },
  { year: "2023", event: "发布“AI营销云”，产品与服务" },
  { year: "2025", event: "发布“AI智，能体构建服务”" },
]

const missionData = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    title: "使命 / Mission",
    description: "数智家居，美好科技"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
    ),
    title: "愿景 / Vision",
    description: "10年成为大家居行业数字化赛道的独角兽"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
    ),
    title: "价值观 / Values",
    description: "用户价值、技术驱动、分享开放、拥抱变化"
  }
]

const advantagesData = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    title: "更专注",
    subtitle: "深耕大家居行业",
    description: "5年深耕大家居行业，深度理解行业痛点，提供更精准的解决方案"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>
    ),
    title: "更专业",
    subtitle: "AI技术赋能",
    description: "30+软件著作权，自主研发AI数字营销平台，技术实力行业领先"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    title: "更贴心",
    subtitle: "全年陪跑服务",
    description: "专业团队7x24小时服务，从策略制定到执行落地全程陪跑"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    title: "更有效",
    subtitle: "数据驱动增长",
    description: "平均为客户实现50%业务增长，数据化运营确保每一分投入都有回报"
  }
]

export default function AboutSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fadeInUp")
          }
        })
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll(".animate-on-scroll")
      elements.forEach((el) => observer.observe(el))
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="about"
      ref={sectionRef}
      className="py-20 lg:py-28 bg-bgPrimary"
    >
      <div className="container mx-auto px-4 lg:px-8">
        
        {/* 公司简介 */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 mb-20">
          <div className="animate-on-scroll">
            <h2 className="text-heading text-techBlue-500 mb-8">关于我们</h2>
          </div>
          <div className="animate-on-scroll">
            <p className="text-body text-textSecondary leading-relaxed mb-6">
              蜜多云科技是一家专注大家居行业的AI数字营销服务商，成立于2018年。我们深度理解大家居行业的营销痛点，
              通过自主研发的AI数字营销平台，为企业提供从战略咨询到执行落地的全年深度陪跑服务。
            </p>
            <p className="text-body text-textSecondary leading-relaxed">
              公司拥有30+软件著作权证书，服务品牌100+，覆盖终端1万+，平均为客户实现50%的业务增长。
              我们致力于用AI技术赋能大家居行业，让每个企业都能享受数字化营销带来的增长红利。
            </p>
          </div>
        </div>

        {/* 发展时间线 */}
        <div className="mb-20">
          <h3 className="animate-on-scroll text-subheading text-textPrimary text-center mb-12">发展历程</h3>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* 时间线 */}
              <div className="absolute left-4 lg:left-1/2 lg:transform lg:-translate-x-1/2 h-full w-px bg-divider"></div>
              
              <div className="space-y-8">
                {timelineData.map((item, index) => (
                  <div key={index} className={`animate-on-scroll flex items-center ${
                    index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                  }`}>
                    <div className={`flex-1 ${index % 2 === 0 ? 'lg:pr-8 lg:text-right' : 'lg:pl-8'}`}>
                      <div className="ml-12 lg:ml-0 bg-white p-6 rounded-lg shadow-card border border-divider">
                        <div className="text-techBlue-500 font-bold text-lg mb-2">{item.year}</div>
                        <div className="text-textSecondary">{item.event}</div>
                      </div>
                    </div>
                    
                    {/* 时间点 */}
                    <div className="absolute left-4 lg:left-1/2 lg:transform lg:-translate-x-1/2 w-3 h-3 bg-techBlue-500 rounded-full border-4 border-white shadow-sm"></div>
                    
                    <div className="flex-1 lg:block hidden"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 使命愿景价值观 */}
        <div className="mb-20">
          <h3 className="animate-on-scroll text-subheading text-textPrimary text-center mb-12">使命·愿景·价值观</h3>
          <div className="grid lg:grid-cols-3 gap-8">
            {missionData.map((item, index) => (
              <div key={index} className="animate-on-scroll text-center group">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-techBlue-50 text-techBlue-500 rounded-xl mb-6 group-hover:bg-techBlue-500 group-hover:text-white transition-all duration-300">
                  {item.icon}
                </div>
                <h4 className="text-xl font-bold text-textPrimary mb-4">{item.title}</h4>
                <p className="text-textSecondary leading-relaxed">{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* 4大核心竞争优势 */}
        <div>
          <h3 className="animate-on-scroll text-subheading text-textPrimary text-center mb-12">4大核心竞争优势</h3>
          <div className="grid lg:grid-cols-2 gap-8">
            {advantagesData.map((item, index) => (
              <div key={index} className="animate-on-scroll group">
                <div className="bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 w-16 h-16 bg-energyOrange-50 text-energyOrange-500 rounded-xl flex items-center justify-center group-hover:bg-energyOrange-500 group-hover:text-white transition-all duration-300">
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-textPrimary mb-2">{item.title}</h4>
                      <div className="text-energyOrange-500 font-medium mb-3">{item.subtitle}</div>
                      <p className="text-textSecondary leading-relaxed">{item.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  )
}
