import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    fontFamily: {
      // 蜜多云科技设计系统字体
      sans: ['"PingFang SC"', '"Source Han Sans"', '"Montserrat"', 'system-ui', 'sans-serif'],
      montserrat: ['Montserrat', 'sans-serif'],
      jost: "Jost, sans-serif", // 保留原有字体以防向后兼容
    },
    extend: {
      // 蜜多云科技色彩系统
      colors: {
        // 主色 - 科技蓝
        techBlue: {
          50: '#e8f2ff',
          100: '#d6e7ff', 
          500: '#2D64B3',
          600: '#245291',
          700: '#1c3f73',
          900: '#152d52',
        },
        // 辅助色 - 活力橙
        energyOrange: {
          50: '#fff4e6',
          100: '#ffe9cc',
          500: '#FF8A00',
          600: '#e67a00',
          700: '#cc6b00',
          900: '#994f00',
        },
        // 文本颜色
        textPrimary: '#333333',
        textSecondary: '#888888',
        // 背景颜色
        bgPrimary: '#FFFFFF',
        bgSecondary: '#F5F7FA',
        // 分隔线颜色
        divider: '#E0E0E0',

        // 保留原有的shadcn/ui色彩系统
        accentColor: "hsl(var(--accent-color))",
        navbar_text_size: "var(--navbar-text-color)",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        baseBackground: "hsl(var(--base-background))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },

      // 蜜多云科技字体大小系统
      fontSize: {
        'display': ['48px', { lineHeight: '1.2', fontWeight: '700' }], // H1
        'heading': ['32px', { lineHeight: '1.3', fontWeight: '700' }], // H2
        'subheading': ['22px', { lineHeight: '1.4', fontWeight: '500' }], // H3
        'body': ['16px', { lineHeight: '1.7', fontWeight: '400' }], // 正文
        'caption': ['14px', { lineHeight: '1.5', fontWeight: '400' }], // 说明文字
      },

      boxShadow: {
        resume_btn_shadow:
          "0 0 5px hsl(var(--accent-color)), 0 0 25px hsl(var(--accent-color)), 0 0 50px hsl(var(--accent-color)), 0 0 200px hsl(var(--accent-color))",
        'card': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'hover': '0 4px 16px rgba(0, 0, 0, 0.15)',
      },

      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },

      // 扩展间距系统
      spacing: {
        '18': '4.5rem',
        '22': '5.5rem',
        '88': '22rem',
        '112': '28rem',
        '128': '32rem',
      },

      keyframes: {
        "frame-contact-me-btn-icon": {
          "0%": {
            transform: "translateX(-100%)",
          },
          "100%": {
            transform: "translateX(0%)",
          },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "infinite-scroll": {
          from: { transform: "translateX(0)" },
          to: { transform: "translateX(-100%)" },
        },
        "fadeInUp": {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "animate-frame-contact-me-btn-icon":
          "frame-contact-me-btn-icon .3s ease",
        "infinite-scroll": "infinite-scroll 40s linear infinite",
        "fadeInUp": "fadeInUp 0.6s ease-out",
      },
    },
    keyframes: {
      'infinite-scroll': {
        from: { transform: 'translateX(0)' },
        // 我们将内容复制了一份，所以移动 50% 的宽度即可实现无缝滚动
        to: { transform: 'translateX(-50%)' },
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function ({ addUtilities }: any) {
      addUtilities({
        ".bg-clip-text": {
          "background-clip": "text",
          "-webkit-background-clip": "text",
        },
      })
    },
  ],
} satisfies Config

export default config
