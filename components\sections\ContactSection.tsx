"use client"

import { useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Music } from "lucide-react"

const contactInfo = [
  {
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
      </svg>
    ),
    title: "联系电话",
    content: "400-123-4567",
    description: "工作时间：9:00-18:00（工作日）"
  },
  {
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    title: "商务邮箱",
    content: "<EMAIL>",
    description: "24小时内回复您的邮件"
  },
  {
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    ),
    title: "公司地址",
    content: "深圳市南山区科技园软件产业基地",
    description: "欢迎莅临指导交流"
  },
  {
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    ),
    title: "微信咨询",
    content: "midoyun2024",
    description: "添加微信，获取专业咨询"
  }
]

const socialLinks = [
  { 
    name: "微信公众号", 
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    )
  },
  { 
    name: "抖音", 
    icon: (
      <Music className="w-5 h-5" />
    )
  },
  { 
    name: "小红书",
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
    )
  },
  { 
    name: "知乎", 
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    )
  }
]

export default function ContactSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fadeInUp")
          }
        })
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll(".animate-on-scroll")
      elements.forEach((el) => observer.observe(el))
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="contact"
      ref={sectionRef}
      className="py-20 lg:py-28 bg-textPrimary text-white"
    >
      <div className="container mx-auto px-4 lg:px-8">
        
        {/* 主要联系区域 */}
        <div className="mb-20">
          {/* 标题区域 */}
          <div className="text-center mb-16">
            <h2 className="animate-on-scroll text-heading text-white mb-6">联系我们</h2>
            <p className="animate-on-scroll text-body text-gray-300 max-w-3xl mx-auto leading-relaxed">
              准备开启您的数字化营销新征程？我们的专业团队随时为您提供咨询服务，
              让我们一起探讨如何为您的企业创造更大的价值
            </p>
          </div>

          {/* 联系方式网格 */}
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <div key={index} className="animate-on-scroll text-center group">
                <div className="bg-white/10 p-8 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20">
                  {/* 图标 */}
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-energyOrange-500 text-white rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    {info.icon}
                  </div>

                  {/* 标题 */}
                  <h3 className="text-xl font-bold text-white mb-3">
                    {info.title}
                  </h3>

                  {/* 内容 */}
                  <div className="text-energyOrange-400 font-medium mb-2">
                    {info.content}
                  </div>

                  {/* 描述 */}
                  <p className="text-gray-400 text-sm">
                    {info.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* CTA 区域 */}
          <div className="animate-on-scroll text-center">
            <div className="bg-white/10 p-12 rounded-2xl border border-white/20 max-w-4xl mx-auto">
              <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
                立即开始您的数字化营销之旅
              </h3>
              <p className="text-gray-300 mb-8 leading-relaxed">
                免费获取专业的数字营销诊断报告，了解您的企业在数字化方面的现状和改进方向
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button className="bg-energyOrange-500 hover:bg-energyOrange-600 text-white px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300 hover:scale-105">
                  免费咨询方案
                </Button>
                <Button 
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-textPrimary px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300"
                >
                  下载企业资料
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 页脚区域 */}
        <div className="border-t border-white/20 pt-12">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-8">
            
            {/* 公司信息 */}
            <div className="animate-on-scroll">
              <h4 className="text-xl font-bold text-white mb-4">蜜多云科技</h4>
              <p className="text-gray-400 mb-6 leading-relaxed">
                专注大家居行业AI数字营销服务，
                用科技赋能传统行业，
                让每个企业都能享受数字化红利。
              </p>
              <div className="flex gap-4">
                {socialLinks.map((social, index) => (
                  <div key={index} title={social.name} className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-energyOrange-500 transition-colors cursor-pointer">
                    {social.icon}
                  </div>
                ))}
              </div>
            </div>

            {/* 产品服务 */}
            <div className="animate-on-scroll">
              <h4 className="text-lg font-bold text-white mb-4">产品服务</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">企业数字营销诊断</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">AI智能营销系统</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">智能获客平台</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">全渠道营销自动化</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">数据智能分析</li>
              </ul>
            </div>

            {/* 解决方案 */}
            <div className="animate-on-scroll">
              <h4 className="text-lg font-bold text-white mb-4">解决方案</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">家具品牌营销</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">建材行业方案</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">定制家居营销</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">家装公司服务</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">装饰材料推广</li>
              </ul>
            </div>

            {/* 关于我们 */}
            <div className="animate-on-scroll">
              <h4 className="text-lg font-bold text-white mb-4">关于我们</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">公司介绍</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">发展历程</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">团队介绍</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">新闻动态</li>
                <li className="hover:text-energyOrange-400 transition-colors cursor-pointer">招贤纳士</li>
              </ul>
            </div>

          </div>

          {/* 版权信息 */}
          <div className="border-t border-white/20 pt-8 text-center">
            <div className="animate-on-scroll">
              <p className="text-gray-400 mb-2">
                © 2024 蜜多云科技有限公司. 保留所有权利.
              </p>
              <p className="text-gray-500 text-sm">
                粤ICP备2024123456号 | 网站建设：蜜多云科技
              </p>
            </div>
          </div>
        </div>

      </div>
    </section>
  )
}
