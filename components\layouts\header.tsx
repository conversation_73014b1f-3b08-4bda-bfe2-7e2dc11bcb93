"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON>et<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, X } from "lucide-react"

const navItems = [
  { name: "首页", href: "#hero" },
  { name: "关于我们", href: "#about" },
  { name: "产品", href: "#products" },
  { name: "案例", href: "#cases" },
  { name: "联系我们", href: "#contact" },
]

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState("hero")
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    const handleSectionChange = () => {
      const sections = navItems.map(item => item.href.substring(1))
      const current = sections.find(section => {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          return rect.top <= 100 && rect.bottom >= 100
        }
        return false
      })
      if (current) {
        setActiveSection(current)
      }
    }

    window.addEventListener("scroll", handleScroll)
    window.addEventListener("scroll", handleSectionChange)
    return () => {
      window.removeEventListener("scroll", handleScroll)
      window.removeEventListener("scroll", handleSectionChange)
    }
  }, [])

  const scrollToSection = (href: string) => {
    const element = document.getElementById(href.substring(1))
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
    setIsOpen(false)
  }

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? "bg-white/95 backdrop-blur-sm shadow-sm border-b border-divider" 
        : "bg-transparent"
    }`}>
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <button
              onClick={() => scrollToSection("#hero")}
              className="text-xl lg:text-2xl font-bold text-techBlue-500 hover:text-techBlue-600 transition-colors"
            >
              蜜多云科技
            </button>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className={`text-base font-medium transition-colors duration-200 hover:text-techBlue-500 ${
                  activeSection === item.href.substring(1)
                    ? "text-techBlue-500"
                    : "text-textSecondary"
                }`}
              >
                {item.name}
              </button>
            ))}
          </nav>

          {/* CTA Button - Desktop */}
          <div className="hidden lg:block">
            <Button
              onClick={() => scrollToSection("#contact")}
              className="bg-energyOrange-500 hover:bg-energyOrange-600 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200"
            >
              免费咨询
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <Button variant="ghost" size="sm" className="p-2">
                <Menu className="h-6 w-6 text-textSecondary" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] bg-white border-l border-divider">
              <div className="flex flex-col h-full">
                {/* Mobile Logo */}
                <div className="flex items-center justify-between py-4 border-b border-divider">
                  <span className="text-xl font-bold text-techBlue-500">蜜多云科技</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="p-2"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex flex-col space-y-4 py-6">
                  {navItems.map((item) => (
                    <button
                      key={item.name}
                      onClick={() => scrollToSection(item.href)}
                      className={`text-left py-3 px-4 rounded-lg text-base font-medium transition-colors ${
                        activeSection === item.href.substring(1)
                          ? "text-techBlue-500 bg-techBlue-50"
                          : "text-textSecondary hover:text-techBlue-500 hover:bg-techBlue-50"
                      }`}
                    >
                      {item.name}
                    </button>
                  ))}
                </nav>

                {/* Mobile CTA */}
                <div className="mt-auto pt-6 border-t border-divider">
                  <Button
                    onClick={() => scrollToSection("#contact")}
                    className="w-full bg-energyOrange-500 hover:bg-energyOrange-600 text-white py-3 rounded-lg font-medium"
                  >
                    免费咨询
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
