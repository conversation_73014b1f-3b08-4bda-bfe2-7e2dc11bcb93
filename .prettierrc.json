{"arrowParens": "always", "semi": false, "tabWidth": 2, "printWidth": 80, "singleQuote": false, "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "endOfLine": "lf", "importOrder": ["^next$", "^react$", "^vite$", "<THIRD_PARTY_MODULES>", "^@/providers", "^@/components/(.*)$", "^[./]", "^[../]"], "importOrderSeparation": false, "importOrderSortSpecifiers": true, "plugins": ["prettier-plugin-tailwindcss", "@trivago/prettier-plugin-sort-imports"]}