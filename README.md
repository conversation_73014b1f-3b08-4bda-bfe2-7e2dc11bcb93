# Shin Thant Portfolio

![shinthant-portfolio](https://i.ibb.co/m5bYtw6/responsive-showcase.png)

![ts](https://badgen.net/badge/Built%20With/TypeScript/blue)

My portfolio website developed with Next.JS(SSG) and TypeScript. Tailwind CSS and GSAP is used for styling and animations. Light & Dark themes supported. Dark is first priotic.

## Features

- Responsive Design 📱💻
- Light & Dark themes ☀️🌙
- Fully Accessible ♿️
- SEO Friendly 🔎

## Tech Stack

**Frontend** - [NextJS](https://nextjs.org/), [React](https://reactjs.org/), [TypeScript](https://www.typescriptlang.org/)  
**Styling** - [Tailwind CSS](https://tailwindcss.com/)  
**Animations** - [GSAP](https://greenstock.com/)  
**Design & Prototype** - [Figma](https://figma.com/)  
**State Management** - [Zustand](https://zustand-demo.pmnd.rs/)  
**Deployment** - [Vercel](https://vercel.com/)

## Lighthouse Score

<a href="https://pagespeed.web.dev/analysis/https-devshinthant-vercel-app/sgswm7q59t?form_factor=desktop">
<img width="630" height="200px" alt="Shin Thant Portfolio Website Lighthouse Score" src="public/lighthouse.svg">
<a>

## Running Locally

Clone the project

```bash
git clone https://github.com/devshinthant/shinthant.dev.git
```

Go to the project directory

```bash
cd shinthant.dev
```

Remove remote origin

```bash
git remote remove origin
```

Install dependencies

```bash
npm install
```

Start the server

```bash
npm run dev
```

## Inspiration and Credits

Here are some inspiration and credits for the design of my portfolio. However I can guarantee that I wrote 100% of the code. These credits are just for ideas and design for my portfolio website.

- [Sat Naing Porfolio](https://satnaing.dev/)
- [Flowtrix](https://www.flowtrix.co/)

## Author

- [@shinthant](https://devshinthant.vercel.app/)
