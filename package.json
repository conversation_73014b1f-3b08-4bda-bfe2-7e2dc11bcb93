{"name": "portfolio_v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "tsc && next build", "start": "next start", "lint": "eslint app --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format:check": "prettier --check .", "format": "prettier --write .", "prepare": "husky install"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@vercel/analytics": "^1.3.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "gsap": "^3.12.5", "iconsax-react": "^0.0.8", "lottie-react": "^2.4.0", "lucide-react": "^0.525.0", "next": "14.2.4", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-rough-notation": "^1.0.5", "split-type": "^0.3.4", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zustand": "^4.5.4"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lottie": "^1.2.10", "eslint": "^8", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"**/*.(js|jsx|ts|tsx|css|scss|md|json)": ["prettier --write"]}}