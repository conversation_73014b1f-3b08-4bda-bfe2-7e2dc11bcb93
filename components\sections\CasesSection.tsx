"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

const achievementsData = [
  {
    number: "100+",
    label: "服务品牌",
    description: "覆盖大家居全产业链品牌"
  },
  {
    number: "1万+",
    label: "覆盖终端",
    description: "全国范围内的门店网络"
  },
  {
    number: "50%",
    label: "平均增长率",
    description: "客户业务平均增长幅度"
  },
  {
    number: "98%",
    label: "客户满意度",
    description: "获得客户一致好评"
  }
]

const partnerLogos = [
  { src: '/assets/images/brand1.png', name: "巨星引擎", category: "数字营销技术深度合作" },
  { src: '/assets/images/brand2.png', name: "通联支付", category: "数字化解决方案" },
  { src: '/assets/images/brand3.png', name: "kuaizi", category: "快消品数字化解决方案合作" },
  { src: '/assets/images/brand4.png', name: "视源股份", category: "智能交互显示技术合作伙伴" },
  { src: '/assets/images/brand5.png', name: "天下秀", category: "社交电商生态战略合作" },
  { src: '/assets/images/brand6.png', name: "萤石", category: "智能家居安防领域战略合作" },
  { src: '/assets/images/brand7.png', name: "有赞", category: "电商SaaS服务战略合作伙伴" }
]

// 将 logo 数据定义为一个数组，便于管理和复用
const logos = [
  { src: '/assets/images/cooperate1.png', alt: '陶陶居 logo' },
  { src: '/assets/images/cooperate2.png', alt: 'SHOPYY logo'},
  { src: '/assets/images/cooperate3.png', alt: '红星美凯龙 Macalline logo'},
  { src: '/assets/images/cooperate4.png', alt: '好利来 Holiland logo'},
  { src: '/assets/images/cooperate5.png', alt: '货拉拉 logo'},
  { src: '/assets/images/cooperate6.png', alt: 'Hampton by Hilton logo'},
  { src: '/assets/images/cooperate7.png', alt: 'AK logo'},
  { src: '/assets/images/cooperate8.png', alt: 'CASGRES logo'},
  { src: '/assets/images/cooperate9.png', alt: 'HAO logo'},
  { src: '/assets/images/cooperate10.png', alt: 'it logo'},
  { src: '/assets/images/cooperate11.png', alt: 'KT logo'},
  { src: '/assets/images/cooperate12.png', alt: '百特 logo'},
  { src: '/assets/images/cooperate13.png', alt: '波拉美 logo'},
  { src: '/assets/images/cooperate14.png', alt: '大角鹿 logo'},
  { src: '/assets/images/cooperate15.png', alt: '迪泉 logo'},
  { src: '/assets/images/cooperate16.png', alt: '帝企鹅 logo'},
  { src: '/assets/images/cooperate17.png', alt: '东宏 logo'},
  { src: '/assets/images/cooperate18.png', alt: '东鹏 logo'},
  { src: '/assets/images/cooperate19.png', alt: '冠星王 logo'},
  { src: '/assets/images/cooperate20.png', alt: '国达 logo'},
  { src: '/assets/images/cooperate21.png', alt: '汇亚 logo'},
  { src: '/assets/images/cooperate22.png', alt: '金巴利 logo'},
  { src: '/assets/images/cooperate23.png', alt: '金科 logo'},
  { src: '/assets/images/cooperate24.png', alt: '金牌亚洲 logo'},
  { src: '/assets/images/cooperate25.png', alt: '孔雀鱼 logo'},
  { src: '/assets/images/cooperate26.png', alt: '来德利2 logo'},
  { src: '/assets/images/cooperate27.png', alt: '揽盛 logo'},
  { src: '/assets/images/cooperate28.png', alt: '利家居 logo'},
  { src: '/assets/images/cooperate29.png', alt: '玛缇 logo'},
  { src: '/assets/images/cooperate30.png', alt: '铭盛 logo'},
  { src: '/assets/images/cooperate31.png', alt: '能强 logo'},
  { src: '/assets/images/cooperate32.png', alt: '狮山 logo'},
  { src: '/assets/images/cooperate33.png', alt: '缇派 logo'},
];

const LogoMarquee = () => {
  return (
    <div className="mb-16">
      <h3 className="text-subheading text-textPrimary text-center mb-12">行业合作品牌</h3>
      
      {/* 
        👇 关键点 1: 在外层容器上添加 `group` 类，标记为悬停事件的触发源。
      */}
      <div className="relative w-full overflow-hidden group">
        
        {/* 
          👇 关键点 2: 在动画容器上添加 `group-hover:paused`。
          当父级的 `group` 被悬停时，这个元素就会应用 `paused` 样式。
        */}
        <div className="flex animate-infinite-scroll group-hover:paused">
          
          {/* 渲染第一遍 logo */}
          {logos.map((logo, index) => (
            <div key={`logo-a-${index}`} className="flex-shrink-0 mx-8 flex items-center justify-center" style={{ height: '64px' }}>
              <Image src={logo.src} alt={logo.alt} width={120} height={56} className="max-h-full w-auto" />
            </div>
          ))}

          {/* 渲染第二遍 logo (实现无缝的关键) */}
          {logos.map((logo, index) => (
            <div key={`logo-b-${index}`} className="flex-shrink-0 mx-8 flex items-center justify-center" style={{ height: '64px' }}>
              <Image src={logo.src} alt={logo.alt} width={120} height={56} className="max-h-full w-auto" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};


export default function CasesSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fadeInUp")
          }
        })
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll(".animate-on-scroll")
      elements.forEach((el) => observer.observe(el))
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="cases"
      ref={sectionRef}
      className="py-20 lg:py-28 bg-bgSecondary"
    >
      <div className="container mx-auto px-4 lg:px-8">
        
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <h2 className="animate-on-scroll text-heading text-techBlue-500 mb-6">案例与成就</h2>
          <p className="animate-on-scroll text-body text-textSecondary max-w-3xl mx-auto leading-relaxed">
            我们与众多知名大家居品牌建立了深度合作关系，通过专业的AI数字营销服务，
            帮助客户实现了显著的业务增长和品牌价值提升
          </p>
        </div>

        {/* 核心成就数据展示 */}
        <div className="mb-20">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {achievementsData.map((item, index) => (
              <div key={index} className="animate-on-scroll text-center group">
                <div className="bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300 hover:-translate-y-1">
                  {/* 大数字 */}
                  <div className="text-4xl lg:text-6xl font-bold text-energyOrange-500 mb-3 group-hover:scale-110 transition-transform duration-300">
                    {item.number}
                  </div>
                  
                  {/* 标签 */}
                  <h3 className="text-xl font-bold text-textPrimary mb-2">
                    {item.label}
                  </h3>
                  
                  {/* 描述 */}
                  <p className="text-textSecondary text-sm">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 行业合作品牌 */}
        <LogoMarquee />

        {/* 战略合作伙伴 */}
        <div className="mb-16">
          <h3 className="animate-on-scroll text-subheading text-textPrimary text-center mb-12">战略合作伙伴</h3>
          <div className="animate-on-scroll bg-white p-8 rounded-xl shadow-card border border-divider">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-8">
              {partnerLogos.map((partner, index) => (
                <div key={index} className="group text-center">
                  {/* Logo 占位符 - 实际项目中应该使用真实的 Logo 图片 */}
                  <div className="w-full h-16 flex items-center justify-center">
                    {/* <span className="text-textSecondary group-hover:text-energyOrange-500 font-medium text-sm transition-colors duration-300">
                      {partner.name}
                    </span> */}
                    <Image src={partner.src} alt={partner.name} width={140} height={65} className="max-h-full w-auto" />
                  </div>
                  <div className="text-xs text-textSecondary">
                    {partner.category}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 成功案例亮点 */}
        <div>
          <h3 className="animate-on-scroll text-subheading text-textPrimary text-center mb-12">成功案例亮点</h3>
          <div className="grid lg:grid-cols-3 gap-8">
            
            {/* 案例1 */}
            <div className="animate-on-scroll bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300">
              <div className="mb-6">
                <div className="w-12 h-12 bg-techBlue-100 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-techBlue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-textPrimary mb-2">SCHENDER 全案营销</h4>
                <p className="text-textSecondary mb-4">通过AI数字营销全案服务，SCHENDER在18个月内实现了线上线下一体化营销体系</p>
                <div className="bg-techBlue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-techBlue-500 mb-1">300%</div>
                  <div className="text-sm text-textSecondary">业绩增长</div>
                </div>
              </div>
            </div>

            {/* 案例2 */}
            <div className="animate-on-scroll bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300">
              <div className="mb-6">
                <div className="w-12 h-12 bg-energyOrange-100 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-textPrimary mb-2">玛缇瓷磗 数字化转型</h4>
                <p className="text-textSecondary mb-4">帮助传统瓷砖品牌完成数字化转型，建立了完整的线上营销生态</p>
                <div className="bg-energyOrange-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-energyOrange-500 mb-1">250%</div>
                  <div className="text-sm text-textSecondary">转化率提升</div>
                </div>
              </div>
            </div>

            {/* 案例3 */}
            <div className="animate-on-scroll bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300">
              <div className="mb-6">
                <div className="w-12 h-12 bg-techBlue-100 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-techBlue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-textPrimary mb-2">100+ 品牌共同成长</h4>
                <p className="text-textSecondary mb-4">服务超过100个大家居品牌，平均帮助客户实现50%以上的业务增长</p>
                <div className="bg-techBlue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-techBlue-500 mb-1">50%</div>
                  <div className="text-sm text-textSecondary">平均增长率</div>
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </section>
  )
} 