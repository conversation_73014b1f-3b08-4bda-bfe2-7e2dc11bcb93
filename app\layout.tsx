import type { Metadata } from "next"
// import { Analytics } from "@vercel/analytics/react"
import { Montserrat } from "next/font/google"
import { ThemeProvider } from "@/providers/ThemeProvider"
// import Loader from "@/components/Loader"
import Header from "@/components/layouts/header"
import "./globals.css"

const montserrat = Montserrat({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "蜜多云科技 - 大家居AI数字营销陪跑领先服务商",
  description: "蜜多云科技专注大家居行业AI数字营销服务，提供企业数字营销全年深度陪跑服务，服务品牌100+，覆盖终端1万+",
  applicationName: "蜜多云科技官网",
  openGraph: {
    type: "website",
    url: "https://midoyun.com/",
    title: "蜜多云科技 - 大家居AI数字营销陪跑领先服务商",
    description:
      "蜜多云科技专注大家居行业AI数字营销服务，提供企业数字营销全年深度陪跑服务，拥有30+软件著作权证书，服务品牌100+",
    siteName: "蜜多云科技官网",
    images: [
      {
        url: "/assets/og-image.jpg",
      },
    ],
  },
  authors: {
    name: "蜜多云科技",
  },
  generator: "NextJs",
  keywords: ["大家居", "AI数字营销", "数字营销服务", "家居行业", "营销陪跑", "蜜多云科技"],
  creator: "蜜多云科技",
  icons: {
    icon: "/favicon.png",
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      {/* <Analytics /> */}
      <body className={`${montserrat.className} font-sans bg-bgPrimary text-textPrimary`}>
        {/* <Loader /> */}

        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <Header />
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
