"use client"

import { useEffect, useRef } from "react"

const credibilityData = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
      </svg>
    ),
    number: "30+",
    title: "软件著作权证书",
    subtitle: "自主知识产权"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
      </svg>
    ),
    number: "15+",
    title: "技术专利申请",
    subtitle: "创新技术保护"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>
    ),
    number: "10+",
    title: "科研院所合作",
    subtitle: "产学研深度融合"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    ),
    number: "20+",
    title: "政府机构认可",
    subtitle: "官方权威认证"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    number: "5+",
    title: "AI算法创新",
    subtitle: "核心技术突破"
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    number: "99.9%",
    title: "系统稳定性",
    subtitle: "7x24小时运行"
  }
]

export default function TechCredibilitySection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fadeInUp")
          }
        })
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll(".animate-on-scroll")
      elements.forEach((el) => observer.observe(el))
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="tech-credibility"
      ref={sectionRef}
      className="py-20 lg:py-28 bg-bgSecondary"
    >
      <div className="container mx-auto px-4 lg:px-8">
        
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <h2 className="animate-on-scroll text-heading text-techBlue-500 mb-6">技术实力</h2>
          <p className="animate-on-scroll text-body text-textSecondary max-w-3xl mx-auto leading-relaxed">
            我们拥有雄厚的技术实力和丰富的行业经验，通过持续的技术创新和产学研合作，
            为大家居行业提供最专业、最可靠的AI数字营销解决方案
          </p>
        </div>

        {/* 技术实力数据网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {credibilityData.map((item, index) => (
            <div key={index} className="animate-on-scroll group">
              <div className="bg-white p-8 rounded-xl shadow-card border border-divider hover:shadow-hover transition-all duration-300 hover:-translate-y-1 text-center">
                
                {/* 图标 */}
                <div className="inline-flex items-center justify-center w-16 h-16 bg-techBlue-50 text-techBlue-500 rounded-xl mb-6 group-hover:bg-techBlue-500 group-hover:text-white transition-all duration-300">
                  {item.icon}
                </div>

                {/* 数字 */}
                <div className="text-4xl lg:text-5xl font-bold text-energyOrange-500 mb-3">
                  {item.number}
                </div>

                {/* 标题 */}
                <h3 className="text-xl font-bold text-textPrimary mb-2">
                  {item.title}
                </h3>

                {/* 副标题 */}
                <p className="text-textSecondary">
                  {item.subtitle}
                </p>

              </div>
            </div>
          ))}
        </div>

        {/* 底部说明 */}
        <div className="mt-16 text-center">
          <div className="animate-on-scroll bg-white p-8 rounded-xl shadow-card border border-divider max-w-4xl mx-auto">
            <h3 className="text-subheading text-techBlue-500 mb-6">权威认证 · 值得信赖</h3>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-bold text-textPrimary mb-3">技术认证</h4>
                <ul className="text-textSecondary space-y-2 text-left">
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    国家高新技术企业认定
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    ISO 9001质量管理体系认证
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    信息安全等级保护三级认证
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-bold text-textPrimary mb-3">合作伙伴</h4>
                <ul className="text-textSecondary space-y-2 text-left">
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    中科院计算技术研究所
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    清华大学人工智能研究院
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-energyOrange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    工信部数字化转型促进中心
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

      </div>
    </section>
  )
} 