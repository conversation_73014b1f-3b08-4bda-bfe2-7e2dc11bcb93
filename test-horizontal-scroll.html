<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GSAP横向滚动测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
        }
        
        .intro {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
        }
        
        .horizontal-section {
            position: relative;
            background: #fff;
        }
        
        .horizontal-container {
            display: flex;
            height: 100vh;
            width: 500vw; /* 5个卡片 * 100vw */
        }
        
        .card {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .card:nth-child(1) { background: #ff6b6b; }
        .card:nth-child(2) { background: #4ecdc4; }
        .card:nth-child(3) { background: #45b7d1; }
        .card:nth-child(4) { background: #96ceb4; }
        .card:nth-child(5) { background: #feca57; }
        
        .outro {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #333;
            color: white;
        }
    </style>
</head>
<body>
    <div class="intro">
        <h1>向下滚动开始横向滚动</h1>
    </div>
    
    <section class="horizontal-section">
        <div class="horizontal-container">
            <div class="card">产品卡片 1</div>
            <div class="card">产品卡片 2</div>
            <div class="card">产品卡片 3</div>
            <div class="card">产品卡片 4</div>
            <div class="card">产品卡片 5</div>
        </div>
    </section>
    
    <div class="outro">
        <h1>横向滚动结束</h1>
    </div>

    <script>
        // 注册ScrollTrigger插件
        gsap.registerPlugin(ScrollTrigger);
        
        // 获取元素
        const horizontalSection = document.querySelector('.horizontal-section');
        const horizontalContainer = document.querySelector('.horizontal-container');
        
        // 创建横向滚动动画
        const scrollTween = gsap.to(horizontalContainer, {
            x: () => -(horizontalContainer.scrollWidth - window.innerWidth),
            ease: "none",
        });
        
        // 创建ScrollTrigger
        ScrollTrigger.create({
            trigger: horizontalSection,
            start: "top top",
            end: () => `+=${horizontalContainer.scrollWidth - window.innerWidth}`,
            pin: true,
            animation: scrollTween,
            scrub: 1,
            invalidateOnRefresh: true,
            anticipatePin: 1,
        });
        
        // 窗口大小改变时刷新
        window.addEventListener('resize', () => {
            ScrollTrigger.refresh();
        });
    </script>
</body>
</html>
