@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --accent-color: 157, 87%, 41%;
    --base-background: 0, 0%, 100%;

    /* Navbar */
    --navbar-text-color: #000000;
    --navbar-text-size: 0.9rem;
    --navbar-text-letter-spacing: -0.14px;
    --navbar-text-line-height: 1.4;
    /* Navbar */

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --accent-color: 157, 87%, 41%;
    --base-background: 193, 20%, 9%;

    /* Navbar */
    --navbar-text-color: #cecece;
    --navbar-text-size: 0.9rem;
    --navbar-text-letter-spacing: -0.14px;
    --navbar-text-line-height: 1.4;
    /* Navbar */

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;
  }
}

@layer base {
  html {
    @apply scroll-smooth;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  ::selection {
    background-color: #05cd77;
    color: #ffffff;
  }
  ::-webkit-selection {
    background-color: #05cd77;
    color: #ffffff;
  }

  ::-moz-selection {
    background-color: #05cd77;
    color: #ffffff;
  }
}

@layer components {
  .navlink {
    position: relative;
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    color: var(--navbar-text-color);
    font-size: var(--navbar-text-size);
    letter-spacing: var(--navbar-text-letter-spacing);
    line-height: var(--navbar-text-line-height);
    transition: color 0.25s ease-out;
    padding: 0.5rem 0;
  }

  .navlink[data-active="true"]::after {
    transform: scaleX(1);
  }

  .navlink:hover {
    @apply dark:text-white;
  }

  .navlink:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
  }

  .navlink::after {
    content: "";
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    left: 0;
    bottom: 0;
    background-color: #05cd77;
    transform-origin: bottom right;
    transition: transform 0.25s ease-out;
  }

  .webkit-reflect {
    -webkit-box-reflect: below 1px linear-gradient(transparent, #0005);
  }

  .contact_me_btn {
    background-image: radial-gradient(
        circle at 50% 85.69%,
        #8fe566,
        rgba(143, 229, 102, 0) 77.6%
      ),
      radial-gradient(
        circle at 50% 111.57%,
        #72e98a,
        rgba(114, 233, 138, 0) 56%
      ),
      radial-gradient(circle at 50% 132.04%, #0acf83, rgba(10, 207, 131, 0) 69%),
      linear-gradient(rgba(126, 249, 139, 0) 21%, #7ef98b 93.49%),
      linear-gradient(#0acf83, #0acf83);
    border-radius: 0.375rem;
    position: relative;
    overflow: hidden;
    box-shadow:
      inset 0 2px 2px rgba(221, 221, 221, 0.5),
      inset 0 -2px #13ae72,
      0 0 0 4px rgba(221, 221, 221, 0.05),
      0 20px 120px -10px rgba(10, 207, 131, 0.8);
  }

  .contact_me_btn_overlay {
    z-index: 1;
    opacity: 0;
    background-image: radial-gradient(
        circle at 176% 143%,
        #8fe566,
        rgba(143, 229, 102, 0) 77%
      ),
      radial-gradient(circle at 138% 79%, #fff, rgba(255, 255, 255, 0)),
      radial-gradient(circle at 50% 254.79%, #fff, rgba(255, 255, 255, 0)),
      linear-gradient(247deg, #7ef98b 48%, rgba(126, 249, 139, 0) 92%),
      linear-gradient(#0acf83, #0acf83);
    transition: opacity 0.2s;
    position: absolute;
    top: 0%;
    bottom: 0%;
    left: 0%;
    right: 0%;
  }
}
